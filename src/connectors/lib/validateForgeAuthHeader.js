import * as jose from 'jose';

const validateForgeAuthHeader = async (authHeader, appId, logger) => {
  let forgeInvocationToken;
  if (authHeader?.startsWith('Bearer')) {
    [, forgeInvocationToken] = authHeader.split(' ');
  }

  if (!forgeInvocationToken) {
    throw new Error('No valid auth token provided in the request');
  }

  logger.info('forgeInvocationToken: ' + JSON.stringify(forgeInvocationToken));

  const jwksUrl = 'https://forge.cdn.prod.atlassian-dev.net/.well-known/jwks.json';
  const JWKS = jose.createRemoteJWKSet(new URL(jwksUrl));

  const jwtHeader = jose.decodeProtectedHeader(forgeInvocationToken);
  logger.info('Attempting to validate the context token: ' + JSON.stringify(jwtHeader));
  const { payload } = await jose.jwtVerify(forgeInvocationToken, JWKS, {
    audience: appId,
    issuer: 'forge/invocation-token',
  });
  logger.info('Successfully validated the context token: ' + JSON.stringify(jwtHeader));
  return payload;
};

export const authMiddleware = async (req, appId, logger) => {
  const { headers } = req;
  let fitClaims;
  try {
    fitClaims = await validateForgeAuthHeader(headers['authorization'], appId, logger);
  } catch (err) {
    logger.error('Error validating context token', err);
    return {
      error: err,
      msg: 'Status: Unauthorized',
    };
  }

  return {
    ...fitClaims,
    accessTokens: {
      system: headers['x-forge-oauth-system'],
      user: headers['x-forge-oauth-user'],
    },
  };
};
