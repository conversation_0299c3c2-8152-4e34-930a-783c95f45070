/** @prettier */
import type { Request<PERSON><PERSON><PERSON> } from 'express';
import { createForgeAuthMiddleware } from '../auth/forge-auth.ts';
import { createUserValidationMiddleware } from '../auth/user-validation.ts';
import { createTokenUpdateMiddleware } from '../auth/token-management.ts';
import { createPlatformDataMiddleware } from '../platform/platform-data.ts';
import con from '../../constants.ts';

/**
 * Creates a composable authentication pipeline based on API type
 *
 * For initialization APIs (open, create): Only forge auth is needed
 * For other APIs: Full pipeline with user validation, platform data, forge auth, and token update
 */
export function createApiAuthPipeline(api: string): RequestHandler[] {
    const initApis = [con.API_OPEN, con.API_CREATE];
    const isInitApi = initApis.some((initApi) => api.includes(initApi));

    if (isInitApi) {
        // Simplified pipeline for initialization APIs
        return [createForgeAuthMiddleware()];
    } else {
        // Full authentication pipeline for regular APIs
        return [
            createUserValidationMiddleware(api),
            createPlatformDataMiddleware(),
            createForgeAuthMiddleware(),
            createTokenUpdateMiddleware(),
        ];
    }
}
